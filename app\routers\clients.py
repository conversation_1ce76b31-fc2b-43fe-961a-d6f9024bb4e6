from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from bson import ObjectId
from ..db import get_db
from ..dependencies import get_current_user
from ..schemas import ClientOut, ProgressSeriesResponse, ProgressPoint
from ..utils import to_object_id, convert_object_ids

router = APIRouter(prefix="/clients", tags=["clients"])


@router.get("", response_model=List[ClientOut])
async def list_clients(current_user: Dict[str, Any] = Depends(get_current_user)):
    db = get_db()
    role = current_user.get("role")
    if role == "parent":
        client_ids = [ObjectId(cid) for cid in current_user.get("client_ids", [])]
        cursor = db.clients.find({"_id": {"$in": client_ids}})
    else:  # therapist
        cursor = db.clients.find({"therapist_id": current_user["_id"]})
    docs = await cursor.to_list(1000)
    return [convert_object_ids(d) | {"_id": str(d["_id"]), "therapist_id": str(d.get("therapist_id")) if d.get("therapist_id") else None} for d in docs]


async def _ensure_can_access_client(client: Optional[Dict[str, Any]], user: Dict[str, Any]):
    if not client:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Client not found")
    role = user.get("role")
    uid = user["_id"]
    if role == "parent":
        allowed = uid in client.get("parent_ids", []) or str(client.get("_id")) in user.get("client_ids", [])
        if not allowed:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Forbidden")
    else:  # therapist
        if client.get("therapist_id") != uid:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Forbidden")


@router.get("/{client_id}", response_model=ClientOut)
async def get_client(client_id: str, current_user: Dict[str, Any] = Depends(get_current_user)):
    db = get_db()
    oid = to_object_id(client_id)
    client = await db.clients.find_one({"_id": oid})
    await _ensure_can_access_client(client, current_user)
    safe = convert_object_ids(client)
    safe["_id"] = str(client["_id"])
    if safe.get("therapist_id"):
        safe["therapist_id"] = str(client.get("therapist_id"))
    return safe


@router.get("/{client_id}/progress", response_model=ProgressSeriesResponse)
async def get_client_progress(client_id: str, current_user: Dict[str, Any] = Depends(get_current_user)):
    db = get_db()
    oid = to_object_id(client_id)
    client = await db.clients.find_one({"_id": oid})
    await _ensure_can_access_client(client, current_user)

    cursor = db.progress.find({"client_id": oid}).sort("date", 1)
    points_docs = await cursor.to_list(1000)
    points: List[ProgressPoint] = []
    for p in points_docs:
        points.append(ProgressPoint(date=p.get("date"), metrics=p.get("metrics", {})))

    return ProgressSeriesResponse(client_id=client_id, points=points)
